'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { Camera, Image, Video, Upload, Download, Eye, Trash2, Search, Grid, List, RefreshCw, RotateCw, CheckCircle, AlertCircle, MoreVertical, X } from 'lucide-react'
import CloudinaryUploadWidget, { CloudinaryUploadResult } from '@/components/CloudinaryUploadWidget'
import { CloudinaryImageGrid, CloudinaryImagePresets } from '@/components/CloudinaryImage'

interface MediaItem {
  id: string
  public_id: string
  url: string
  secure_url: string
  format: string
  resource_type: string
  width?: number
  height?: number
  bytes: number
  created_at: string
  tags: string[]
  folder?: string
  original_filename?: string
  version: number
  signature: string
  etag: string
  type: string
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
  has_next: boolean
  has_prev: boolean
  next_cursor?: string
  prev_cursor?: string
}

interface MediaStats {
  total_images: number
  total_videos: number
  total_files: number
  total_size: number
}

interface SyncStatus {
  last_sync: string
  is_synced: boolean
  pending_operations: number
}

export default function MediaCenterPage() {
  // View and UI state
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Data state
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([])
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 50,
    total: 0,
    pages: 0,
    has_next: false,
    has_prev: false
  })
  const [stats, setStats] = useState<MediaStats>({
    total_images: 0,
    total_videos: 0,
    total_files: 0,
    total_size: 0
  })
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    last_sync: '',
    is_synced: true,
    pending_operations: 0
  })

  // Loading states
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [isSyncing, setIsSyncing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // Infinite scroll
  const [hasMore, setHasMore] = useState(true)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const loadMoreRef = useRef<HTMLDivElement | null>(null)

  /**
   * Load media items with pagination and filtering
   */
  const loadMediaItems = useCallback(async (page: number = 1, append: boolean = false) => {
    if (append) {
      setIsLoadingMore(true)
    } else {
      setIsLoading(true)
    }

    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString()
      })

      if (searchQuery) {
        params.append('search', searchQuery)
      }

      // Fetch from enhanced API endpoint
      const response = await fetch(`/api/cloudinary/media?${params}`)

      if (response.ok) {
        const data = await response.json()

        if (append) {
          // Append new items for infinite scroll
          setMediaItems(prev => [...prev, ...(data.items || [])])
        } else {
          // Replace items for new search/refresh
          setMediaItems(data.items || [])
        }

        // Update pagination info
        setPagination(data.pagination || {
          page: 1,
          limit: 50,
          total: 0,
          pages: 0,
          has_next: false,
          has_prev: false
        })

        // Update stats
        setStats(data.stats || {
          total_images: 0,
          total_videos: 0,
          total_files: 0,
          total_size: 0
        })

        // Update sync status
        setSyncStatus(data.sync_status || {
          last_sync: '',
          is_synced: true,
          pending_operations: 0
        })

        // Update hasMore for infinite scroll
        setHasMore(data.pagination?.has_next || false)

      } else {
        console.warn('API failed, using empty state')
        if (!append) {
          setMediaItems([])
          setStats({
            total_images: 0,
            total_videos: 0,
            total_files: 0,
            total_size: 0
          })
        }
        setHasMore(false)
      }
    } catch (error) {
      console.error('Failed to load media items:', error)
      if (!append) {
        setMediaItems([])
        setStats({
          total_images: 0,
          total_videos: 0,
          total_files: 0,
          total_size: 0
        })
      }
      setHasMore(false)
    } finally {
      setIsLoading(false)
      setIsLoadingMore(false)
    }
  }, [pagination.limit, searchQuery])

  /**
   * Load more items for infinite scroll
   */
  const loadMoreItems = useCallback(() => {
    if (!isLoadingMore && hasMore && pagination.has_next) {
      loadMediaItems(pagination.page + 1, true)
    }
  }, [isLoadingMore, hasMore, pagination.has_next, pagination.page, loadMediaItems])

  /**
   * Sync with Cloudinary
   */
  const syncWithCloudinary = async () => {
    setIsSyncing(true)
    try {
      const response = await fetch('/api/cloudinary/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Sync completed:', data)

        // Refresh media items after sync
        await loadMediaItems(1, false)

        // Show success message (you could add a toast notification here)
        console.log(`✅ Sync completed: ${data.data.synced_items} items synced`)
      } else {
        console.error('Sync failed:', await response.text())
      }
    } catch (error) {
      console.error('Sync error:', error)
    } finally {
      setIsSyncing(false)
    }
  }

  /**
   * Delete selected items
   */
  const deleteSelectedItems = async () => {
    if (selectedItems.length === 0) return

    setIsDeleting(true)
    try {
      const params = new URLSearchParams({
        public_ids: selectedItems.join(',')
      })

      const response = await fetch(`/api/cloudinary/media?${params}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Delete completed:', data)

        // Remove deleted items from state
        setMediaItems(prev => prev.filter(item => !selectedItems.includes(item.public_id)))
        setSelectedItems([])
        setShowBulkActions(false)

        // Update stats
        const deletedItems = mediaItems.filter(item => selectedItems.includes(item.public_id))
        const deletedSize = deletedItems.reduce((sum, item) => sum + item.bytes, 0)
        const deletedImages = deletedItems.filter(item => item.resource_type === 'image').length
        const deletedVideos = deletedItems.filter(item => item.resource_type === 'video').length

        setStats(prev => ({
          total_images: Math.max(0, prev.total_images - deletedImages),
          total_videos: Math.max(0, prev.total_videos - deletedVideos),
          total_files: Math.max(0, prev.total_files - deletedItems.length),
          total_size: Math.max(0, prev.total_size - deletedSize)
        }))

        console.log(`✅ Deleted ${data.deleted?.length || 0} items`)
      } else {
        console.error('Delete failed:', await response.text())
      }
    } catch (error) {
      console.error('Delete error:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  /**
   * Handle successful upload
   */
  const handleUploadSuccess = (result: CloudinaryUploadResult) => {
    console.log('Upload successful:', result)

    // Add new item to the list
    const newItem: MediaItem = {
      id: result.info.public_id,
      public_id: result.info.public_id,
      url: result.info.url,
      secure_url: result.info.secure_url,
      format: result.info.format,
      resource_type: result.info.resource_type,
      width: result.info.width,
      height: result.info.height,
      bytes: result.info.bytes,
      created_at: result.info.created_at,
      tags: result.info.tags,
      folder: result.info.folder,
      original_filename: result.info.original_filename,
      version: result.info.version || 1,
      signature: result.info.signature || '',
      etag: result.info.etag || '',
      type: result.info.type || 'upload'
    }

    setMediaItems(prev => [newItem, ...prev])

    // Update stats
    setStats(prev => ({
      total_images: result.info.resource_type === 'image' ? prev.total_images + 1 : prev.total_images,
      total_videos: result.info.resource_type === 'video' ? prev.total_videos + 1 : prev.total_videos,
      total_files: prev.total_files + 1,
      total_size: prev.total_size + result.info.bytes
    }))

    // Update pagination total
    setPagination(prev => ({
      ...prev,
      total: prev.total + 1
    }))
  }

  /**
   * Handle upload error
   */
  const handleUploadError = (error: any) => {
    console.error('Upload failed:', error)
    // You could show a toast notification here
  }

  /**
   * Handle search with debouncing
   */
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query)
    // Reset pagination and reload
    setPagination(prev => ({ ...prev, page: 1 }))
    loadMediaItems(1, false)
  }, [loadMediaItems])

  /**
   * Toggle item selection
   */
  const toggleItemSelection = (publicId: string) => {
    setSelectedItems(prev => {
      const newSelection = prev.includes(publicId)
        ? prev.filter(id => id !== publicId)
        : [...prev, publicId]

      setShowBulkActions(newSelection.length > 0)
      return newSelection
    })
  }

  /**
   * Select all visible items
   */
  const selectAllItems = () => {
    const allIds = mediaItems.map(item => item.public_id)
    setSelectedItems(allIds)
    setShowBulkActions(true)
  }

  /**
   * Clear selection
   */
  const clearSelection = () => {
    setSelectedItems([])
    setShowBulkActions(false)
  }

  /**
   * Format file size
   */
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // ==================== EFFECTS ====================

  /**
   * Initialize component
   */
  useEffect(() => {
    loadMediaItems(1, false)
  }, [])

  /**
   * Setup infinite scroll observer
   */
  useEffect(() => {
    if (!loadMoreRef.current) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {
          loadMoreItems()
        }
      },
      { threshold: 0.1 }
    )

    observerRef.current.observe(loadMoreRef.current)

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [hasMore, isLoadingMore, loadMoreItems])

  /**
   * Handle search debouncing
   */
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery !== '') {
        handleSearch(searchQuery)
      }
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchQuery, handleSearch])

  /**
   * Filter media items based on search query (client-side filtering for immediate feedback)
   */
  const filteredItems = searchQuery
    ? mediaItems.filter(item =>
        item.original_filename?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.public_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : mediaItems

  // Load media items on component mount
  useEffect(() => {
    loadMediaItems()
  }, [])

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl shadow-lg p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Media Center</h1>
            <p className="text-purple-100 text-lg">Manage photos, videos, and media files</p>
          </div>
          <div className="hidden lg:block">
            <div className="h-20 w-20 bg-white/20 rounded-2xl flex items-center justify-center">
              <Camera className="h-10 w-10 text-white" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-purple-100">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 rounded-xl">
              <Image className="h-6 w-6 text-purple-600" aria-label="Images icon" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Images</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total_images.toLocaleString()}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-pink-100">
          <div className="flex items-center">
            <div className="p-3 bg-pink-100 rounded-xl">
              <Video className="h-6 w-6 text-pink-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Videos</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total_videos.toLocaleString()}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-blue-100">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 rounded-xl">
              <Upload className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Files</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total_files.toLocaleString()}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-green-100">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 rounded-xl">
              <Download className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Storage Used</p>
              <p className="text-2xl font-bold text-gray-900">{formatFileSize(stats.total_size)}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search media files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-purple-100 text-purple-600' : 'text-gray-400'}`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-purple-100 text-purple-600' : 'text-gray-400'}`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* Sync Status Indicator */}
            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg">
              {syncStatus.is_synced ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-yellow-500" />
              )}
              <span className="text-xs text-gray-600">
                {syncStatus.is_synced ? 'Synced' : `${syncStatus.pending_operations} pending`}
              </span>
            </div>

            {/* Sync Button */}
            <button
              onClick={syncWithCloudinary}
              disabled={isSyncing}
              className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              title="Sync all files from Cloudinary to database"
            >
              <RotateCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
              {isSyncing ? 'Syncing...' : 'Sync from Cloudinary'}
            </button>

            {/* Refresh Button */}
            <button
              onClick={() => loadMediaItems(1, false)}
              disabled={isLoading}
              className="inline-flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>

            {/* Upload Widget */}
            <CloudinaryUploadWidget
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
              folder="lgu-uploads/media"
              multiple={true}
              maxFiles={10}
              buttonText="Upload Media"
              variant="primary"
            />
          </div>
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {showBulkActions && (
        <div className="bg-blue-50 border border-blue-200 rounded-2xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-900">
                {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} selected
              </span>
              <button
                onClick={selectAllItems}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Select All ({mediaItems.length})
              </button>
              <button
                onClick={clearSelection}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                Clear Selection
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={deleteSelectedItems}
                disabled={isDeleting}
                className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                <Trash2 className={`h-4 w-4 mr-2 ${isDeleting ? 'animate-pulse' : ''}`} />
                {isDeleting ? 'Deleting...' : 'Delete Selected'}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Media Library
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {pagination.total > 0 && (
                <>
                  Showing {mediaItems.length} of {pagination.total.toLocaleString()} items
                  {searchQuery && ` matching "${searchQuery}"`}
                </>
              )}
            </p>
          </div>
          {isLoading && (
            <div className="flex items-center text-sm text-gray-500">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Loading...
            </div>
          )}
        </div>

        {filteredItems.length === 0 && !isLoading ? (
          <div className="text-center py-12">
            <Image className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No media files found</h4>
            <p className="text-gray-500 mb-6">
              {searchQuery ? 'Try adjusting your search terms.' : 'Upload your first media file to get started.'}
            </p>
            {!searchQuery && (
              <div className="space-y-4">
                <CloudinaryUploadWidget
                  onUploadSuccess={handleUploadSuccess}
                  onUploadError={handleUploadError}
                  folder="lgu-uploads/media"
                  multiple={true}
                  maxFiles={10}
                  buttonText="Upload Your First Media"
                  variant="primary"
                />
                <div className="text-sm text-gray-500 space-y-2">
                  <p>✨ Upload images to test the Cloudinary integration</p>
                  <p>📁 Files will be stored in your Cloudinary Media Library</p>
                  <p>🔄 Perfect bidirectional sync with Cloudinary</p>
                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-yellow-800 font-medium">📋 Setup Required:</p>
                    <p className="text-yellow-700 text-xs mt-1">
                      If files don't persist after refresh, run the database setup script from
                      <code className="bg-yellow-100 px-1 rounded">docs/full-complete-supabase-script.md</code>
                    </p>
                    <p className="text-yellow-700 text-xs mt-1">
                      Then click "Sync from Cloudinary" to import existing files.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {/* Media Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {filteredItems.map((item) => (
                <div
                  key={item.id}
                  className={`group relative bg-white rounded-lg border-2 overflow-hidden hover:shadow-lg transition-all cursor-pointer ${
                    selectedItems.includes(item.public_id)
                      ? 'border-blue-500 ring-2 ring-blue-200'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => toggleItemSelection(item.public_id)}
                >
                  {/* Selection Checkbox */}
                  <div className="absolute top-2 left-2 z-10">
                    <div className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all ${
                      selectedItems.includes(item.public_id)
                        ? 'bg-blue-500 border-blue-500'
                        : 'bg-white border-gray-300 group-hover:border-gray-400'
                    }`}>
                      {selectedItems.includes(item.public_id) && (
                        <CheckCircle className="w-3 h-3 text-white" />
                      )}
                    </div>
                  </div>

                  {/* Media Preview */}
                  <div className="aspect-square bg-gray-100">
                    {item.public_id ? (
                      <CloudinaryImagePresets.Media
                        src={item.public_id}
                        alt={item.original_filename || 'Media file'}
                        width={300}
                        height={300}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Image className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Media Info */}
                  <div className="p-3">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {item.original_filename || item.public_id}
                    </h4>
                    <p className="text-xs text-gray-500 mt-1">
                      {item.format?.toUpperCase()} • {formatFileSize(item.bytes)}
                    </p>
                    {item.width && item.height && (
                      <p className="text-xs text-gray-400">
                        {item.width} × {item.height}
                      </p>
                    )}
                  </div>

                  {/* Hover Actions */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          window.open(item.secure_url, '_blank')
                        }}
                        className="p-2 bg-white rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          const link = document.createElement('a')
                          link.href = item.secure_url
                          link.download = item.original_filename || item.public_id
                          link.click()
                        }}
                        className="p-2 bg-white rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Infinite Scroll Trigger */}
            {hasMore && (
              <div ref={loadMoreRef} className="flex justify-center py-8">
                {isLoadingMore ? (
                  <div className="flex items-center space-x-2 text-gray-500">
                    <RefreshCw className="h-5 w-5 animate-spin" />
                    <span>Loading more...</span>
                  </div>
                ) : (
                  <button
                    onClick={loadMoreItems}
                    className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Load More
                  </button>
                )}
              </div>
            )}

            {/* End of Results */}
            {!hasMore && filteredItems.length > 0 && (
              <div className="text-center py-8 text-gray-500">
                <p>You've reached the end of your media library</p>
                <p className="text-sm mt-1">
                  {pagination.total.toLocaleString()} total items
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
