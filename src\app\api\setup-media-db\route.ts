/**
 * Setup Media Database API Route
 * 
 * Verifies and initializes the media library database setup.
 * Ensures all tables, functions, and configurations are properly set up
 * for bidirectional sync with Cloudinary.
 * 
 * Features:
 * - 🔍 Database schema verification
 * - 🛠️ Function availability checks
 * - 📊 Initial statistics gathering
 * - 🔄 Sync status validation
 * - 🚀 Ready-to-use confirmation
 * 
 * <AUTHOR> Project Team
 * @version 1.0.0
 */

import { NextRequest, NextResponse } from 'next/server'
import { SupabaseMediaService } from '@/lib/supabaseMediaService'
import { createClient } from '@supabase/supabase-js'

/**
 * GET /api/setup-media-db
 * Verify media database setup and configuration
 */
export async function GET(request: NextRequest) {
  try {
    console.log('[Setup Media DB] Verifying database setup...')

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    const setupStatus = {
      database_connected: false,
      tables_exist: false,
      functions_exist: false,
      indexes_exist: false,
      rls_enabled: false,
      initial_stats: null as any,
      errors: [] as string[],
      warnings: [] as string[]
    }

    // 1. Test database connection
    try {
      const { data, error } = await supabase.from('media_assets').select('count').limit(1)
      if (error) throw error
      setupStatus.database_connected = true
      console.log('[Setup Media DB] ✅ Database connection successful')
    } catch (error) {
      setupStatus.errors.push(`Database connection failed: ${error}`)
      console.error('[Setup Media DB] ❌ Database connection failed:', error)
    }

    // 2. Check if required tables exist
    try {
      const { data: tables, error } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .in('table_name', [
          'media_assets',
          'media_sync_log', 
          'media_usage',
          'media_collections',
          'media_collection_items'
        ])

      if (error) throw error

      const requiredTables = ['media_assets', 'media_sync_log', 'media_usage', 'media_collections', 'media_collection_items']
      const existingTables = tables?.map(t => t.table_name) || []
      const missingTables = requiredTables.filter(table => !existingTables.includes(table))

      if (missingTables.length === 0) {
        setupStatus.tables_exist = true
        console.log('[Setup Media DB] ✅ All required tables exist')
      } else {
        setupStatus.errors.push(`Missing tables: ${missingTables.join(', ')}`)
        console.error('[Setup Media DB] ❌ Missing tables:', missingTables)
      }
    } catch (error) {
      setupStatus.errors.push(`Table verification failed: ${error}`)
      console.error('[Setup Media DB] ❌ Table verification failed:', error)
    }

    // 3. Check if required functions exist
    try {
      const { data: functions, error } = await supabase
        .from('information_schema.routines')
        .select('routine_name')
        .eq('routine_schema', 'public')
        .in('routine_name', [
          'soft_delete_media_asset',
          'restore_media_asset',
          'update_media_sync_status',
          'get_media_statistics',
          'cleanup_old_sync_logs'
        ])

      if (error) throw error

      const requiredFunctions = [
        'soft_delete_media_asset',
        'restore_media_asset', 
        'update_media_sync_status',
        'get_media_statistics',
        'cleanup_old_sync_logs'
      ]
      const existingFunctions = functions?.map(f => f.routine_name) || []
      const missingFunctions = requiredFunctions.filter(func => !existingFunctions.includes(func))

      if (missingFunctions.length === 0) {
        setupStatus.functions_exist = true
        console.log('[Setup Media DB] ✅ All required functions exist')
      } else {
        setupStatus.errors.push(`Missing functions: ${missingFunctions.join(', ')}`)
        console.error('[Setup Media DB] ❌ Missing functions:', missingFunctions)
      }
    } catch (error) {
      setupStatus.errors.push(`Function verification failed: ${error}`)
      console.error('[Setup Media DB] ❌ Function verification failed:', error)
    }

    // 4. Check indexes
    try {
      const { data: indexes, error } = await supabase
        .from('pg_indexes')
        .select('indexname')
        .eq('schemaname', 'public')
        .like('indexname', 'idx_media_%')

      if (error) throw error

      const mediaIndexes = indexes?.length || 0
      if (mediaIndexes >= 10) { // We expect at least 10 media-related indexes
        setupStatus.indexes_exist = true
        console.log(`[Setup Media DB] ✅ Found ${mediaIndexes} media indexes`)
      } else {
        setupStatus.warnings.push(`Only ${mediaIndexes} media indexes found, expected at least 10`)
        console.warn(`[Setup Media DB] ⚠️ Only ${mediaIndexes} media indexes found`)
      }
    } catch (error) {
      setupStatus.warnings.push(`Index verification failed: ${error}`)
      console.warn('[Setup Media DB] ⚠️ Index verification failed:', error)
    }

    // 5. Test RLS policies
    try {
      // Try to access media_assets with authenticated role simulation
      const { data, error } = await supabase
        .from('media_assets')
        .select('count')
        .limit(1)

      if (!error) {
        setupStatus.rls_enabled = true
        console.log('[Setup Media DB] ✅ RLS policies working')
      }
    } catch (error) {
      setupStatus.warnings.push(`RLS verification failed: ${error}`)
      console.warn('[Setup Media DB] ⚠️ RLS verification failed:', error)
    }

    // 6. Get initial statistics
    try {
      const stats = await SupabaseMediaService.getMediaStats()
      setupStatus.initial_stats = stats
      console.log('[Setup Media DB] ✅ Statistics retrieved:', stats)
    } catch (error) {
      setupStatus.warnings.push(`Statistics retrieval failed: ${error}`)
      console.warn('[Setup Media DB] ⚠️ Statistics retrieval failed:', error)
    }

    // Determine overall status
    const isFullySetup = setupStatus.database_connected && 
                        setupStatus.tables_exist && 
                        setupStatus.functions_exist &&
                        setupStatus.errors.length === 0

    const response = {
      success: isFullySetup,
      message: isFullySetup 
        ? '🎉 Media library database is fully configured and ready for bidirectional sync!'
        : '⚠️ Media library database setup has issues that need attention',
      setup_status: setupStatus,
      next_steps: isFullySetup ? [
        '✅ Database is ready for production use',
        '🔄 Test the sync functionality in admin panel',
        '📱 Configure Cloudinary webhooks for real-time sync',
        '🚀 Start uploading and managing media files'
      ] : [
        '📋 Run the complete Supabase script from docs/full-complete-supabase-script.md',
        '🔍 Check database connection and permissions',
        '🛠️ Verify all environment variables are set correctly',
        '🔄 Re-run this setup verification'
      ],
      environment_check: {
        supabase_url: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        supabase_service_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
        cloudinary_cloud_name: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        cloudinary_api_key: !!process.env.CLOUDINARY_API_KEY,
        cloudinary_api_secret: !!process.env.CLOUDINARY_API_SECRET
      },
      timestamp: new Date().toISOString()
    }

    console.log(`[Setup Media DB] Setup verification completed. Success: ${isFullySetup}`)
    
    return NextResponse.json(response)

  } catch (error) {
    console.error('[Setup Media DB] Setup verification failed:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Setup verification failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

/**
 * POST /api/setup-media-db
 * Initialize or repair media database setup
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[Setup Media DB] Attempting to initialize database setup...')

    return NextResponse.json({
      success: false,
      message: '🔧 Automatic database setup is not available for security reasons',
      instructions: [
        '📋 **REQUIRED**: Run the complete SQL script manually',
        '1️⃣ Copy the entire script from: docs/full-complete-supabase-script.md',
        '2️⃣ Open your Supabase dashboard → SQL Editor',
        '3️⃣ Paste and execute the complete script',
        '4️⃣ Verify all tables are created successfully',
        '5️⃣ Run GET /api/setup-media-db to verify setup',
        '6️⃣ Test the media library sync functionality'
      ],
      why_manual: [
        '🔒 Database schema changes require admin privileges',
        '🛡️ Prevents accidental data loss or corruption',
        '📊 Ensures proper constraints and indexes are created',
        '🔄 Guarantees complete bidirectional sync setup'
      ],
      script_location: 'docs/full-complete-supabase-script.md',
      verification_endpoint: '/api/setup-media-db (GET)',
      manual_setup_required: true,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('[Setup Media DB] Setup request failed:', error)

    return NextResponse.json({
      success: false,
      message: 'Setup request failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
